package com.yuelan.hermes.quanyi.biz.service;

import com.yuelan.hermes.quanyi.common.constant.RedisKeys;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccNcOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccOuterChannelDO;
import com.yuelan.plugins.redisson.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 扣量计算服务
 * 负责订单创建时的扣量计算逻辑
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Slf4j
@Service
public class DeductionCalculationService {
    @Autowired
    private ChannelHierarchyService channelHierarchyService;

    @Autowired
    private EccOuterChannelDOService eccOuterChannelDOService;

    // 常量定义
    private static final int DEDUCTION_CYCLE_SIZE = 20; // 扣量周期大小
    private static final int CHANNEL_DISABLED_FLAG = 1; // 渠道停用标识
    private static final String PLATFORM_ATTRIBUTION = "平台"; // 平台归属标识
    // 扣量比例对应的扣量位置映射表
    private static final Map<Integer, List<Integer>> DEDUCTION_POSITION_MAP = new HashMap<>();

    static {
        // 初始化扣量位置映射表（基于 1-20 的位置）
        DEDUCTION_POSITION_MAP.put(5, Arrays.asList(20));
        DEDUCTION_POSITION_MAP.put(10, Arrays.asList(10, 20));
        DEDUCTION_POSITION_MAP.put(15, Arrays.asList(6, 13, 20));
        DEDUCTION_POSITION_MAP.put(20, Arrays.asList(5, 10, 15, 20));
        DEDUCTION_POSITION_MAP.put(25, Arrays.asList(4, 8, 12, 16, 20));
        DEDUCTION_POSITION_MAP.put(30, Arrays.asList(3, 7, 9, 13, 17, 19));
        DEDUCTION_POSITION_MAP.put(35, Arrays.asList(3, 7, 9, 11, 13, 17, 19));
        DEDUCTION_POSITION_MAP.put(40, Arrays.asList(2, 4, 6, 8, 12, 14, 16, 18));
        DEDUCTION_POSITION_MAP.put(45, Arrays.asList(2, 4, 6, 8, 10, 12, 14, 16, 18));
        DEDUCTION_POSITION_MAP.put(50, Arrays.asList(2, 4, 6, 8, 10, 12, 14, 16, 18, 20));
        DEDUCTION_POSITION_MAP.put(55, Arrays.asList(2, 4, 6, 8, 10, 11, 13, 15, 16, 17, 19));
        DEDUCTION_POSITION_MAP.put(60, Arrays.asList(2, 4, 5, 7, 8, 10, 12, 14, 15, 17, 18, 20));
        DEDUCTION_POSITION_MAP.put(65, Arrays.asList(2, 4, 5, 7, 8, 10, 11, 13, 14, 16, 17, 19, 20));
        DEDUCTION_POSITION_MAP.put(70, Arrays.asList(2, 3, 4, 6, 7, 8, 10, 12, 13, 14, 16, 17, 18, 20));
        DEDUCTION_POSITION_MAP.put(75, Arrays.asList(2, 3, 4, 6, 7, 8, 10, 11, 12, 14, 15, 16, 18, 19, 20));
        DEDUCTION_POSITION_MAP.put(80, Arrays.asList(2, 3, 4, 5, 7, 8, 9, 10, 12, 13, 14, 15, 17, 18, 19, 20));
        DEDUCTION_POSITION_MAP.put(85, Arrays.asList(2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20));
        DEDUCTION_POSITION_MAP.put(90, Arrays.asList(2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20));
        DEDUCTION_POSITION_MAP.put(95, Arrays.asList(2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20));

        // 验证扣量位置配置的正确性
        for (Map.Entry<Integer, List<Integer>> entry : DEDUCTION_POSITION_MAP.entrySet()) {
            Integer rate = entry.getKey();
            List<Integer> positions = entry.getValue();

            if (positions == null || positions.isEmpty()) {
                throw new IllegalStateException("扣量比例" + rate + "%的位置配置为空");
            }

            // 验证位置数量是否与比例匹配
            int expectedCount = rate * DEDUCTION_CYCLE_SIZE / 100;
            if (positions.size() != expectedCount) {
                throw new IllegalStateException(String.format(
                        "扣量比例%d%%的位置配置数量不正确，期望%d个，实际%d个",
                        rate, expectedCount, positions.size()));
            }

            // 验证位置范围是否正确
            for (Integer position : positions) {
                if (position < 1 || position > DEDUCTION_CYCLE_SIZE) {
                    throw new IllegalStateException(String.format(
                            "扣量比例%d%%的位置配置超出范围，位置%d不在[1,%d]范围内",
                            rate, position, DEDUCTION_CYCLE_SIZE));
                }
            }
        }
        log.info("扣量位置映射表验证通过，共配置{}种扣量比例", DEDUCTION_POSITION_MAP.size());
    }

    /**
     * 计算订单的扣量渠道
     * 按照漏斗式计算：从平台开始，逐级向下计算扣量比例，首次扣量成功的渠道即为扣量渠道
     *
     * @param order 订单对象
     * @return 扣量渠道对象，如果没有扣量则返回null
     */
    public EccOuterChannelDO calculateDeductionChannel(EccNcOrderDO order) {
        long startTime = System.currentTimeMillis();

        try {
            // 1. 参数验证
            if (!validateOrderForDeduction(order)) {
                return null;
            }

            Long developmentChannelId = order.getChannelId();
            String orderNo = order.getOrderNo();

            log.info("开始计算订单扣量，订单号: {}, 发展渠道ID: {}", orderNo, developmentChannelId);

            // 2. 构建扣量检查链条
            List<EccOuterChannelDO> deductionChain = buildDeductionChain(developmentChannelId);
            if (deductionChain.isEmpty()) {
                log.warn("无法构建扣量链条，订单号: {}, 发展渠道ID: {}", orderNo, developmentChannelId);
                return null;
            }

            // 3. 执行扣量检查
            EccOuterChannelDO deductionResult = executeDeductionCheck(deductionChain, developmentChannelId, orderNo);

            long executionTime = System.currentTimeMillis() - startTime;
            log.info("扣量计算完成，订单号: {}, 耗时: {}ms, 结果: {}",
                    orderNo, executionTime,
                    deductionResult != null ? deductionResult.getChannelName() : "未扣量");

            return deductionResult;

        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            log.error("扣量计算异常，订单号: {}, 耗时: {}ms",
                    order != null ? order.getOrderNo() : "unknown", executionTime, e);
            return null;
        }
    }

    /**
     * 验证订单是否满足扣量计算条件
     *
     * @param order 订单对象
     * @return true表示验证通过
     */
    private boolean validateOrderForDeduction(EccNcOrderDO order) {
        if (order == null) {
            log.warn("订单对象为空，无法计算扣量");
            return false;
        }

        if (order.getChannelId() == null) {
            log.warn("订单发展渠道ID为空，无法计算扣量，订单号: {}", order.getOrderNo());
            return false;
        }

        if (order.getOrderNo() == null || order.getOrderNo().trim().isEmpty()) {
            log.warn("订单号为空，无法计算扣量，渠道ID: {}", order.getChannelId());
            return false;
        }

        return true;
    }

    /**
     * 构建扣量检查链条
     *
     * @param developmentChannelId 发展渠道ID
     * @return 扣量检查链条，从根级到发展渠道
     */
    private List<EccOuterChannelDO> buildDeductionChain(Long developmentChannelId) {
        try {
            // 获取发展渠道的完整上级链路（不包含发展渠道本身）
            // 例如：A2-1 → 返回 [A, A2]（从根级到直接父级，level从小到大）
            List<EccOuterChannelDO> parentChain = channelHierarchyService.getParentChannelChain(developmentChannelId);

            // 获取发展渠道
            EccOuterChannelDO developmentChannel = eccOuterChannelDOService.getById(developmentChannelId);
            if (developmentChannel == null) {
                log.warn("发展渠道不存在，渠道ID: {}", developmentChannelId);
                return Collections.emptyList();
            }

            // 构建完整的扣量检查链条：[A, A2, A2-1]（从根级到发展渠道）
            // parentChain已经是正确顺序[A, A2]，直接使用并添加发展渠道
            List<EccOuterChannelDO> deductionChain = new ArrayList<>(parentChain.size() + 1);
            deductionChain.addAll(parentChain);
            deductionChain.add(developmentChannel);

            log.debug("构建扣量检查链条成功，链条长度: {}, 链条: {}",
                    deductionChain.size(),
                    deductionChain.stream().map(EccOuterChannelDO::getChannelName).collect(Collectors.toList()));

            return deductionChain;

        } catch (Exception e) {
            log.error("构建扣量链条异常，发展渠道ID: {}", developmentChannelId, e);
            return Collections.emptyList();
        }
    }

    /**
     * 执行扣量检查逻辑
     *
     * @param deductionChain       扣量检查链条
     * @param developmentChannelId 发展渠道ID
     * @param orderNo              订单号
     * @return 扣量渠道对象，如果没有扣量则返回null
     */
    private EccOuterChannelDO executeDeductionCheck(List<EccOuterChannelDO> deductionChain,
                                                    Long developmentChannelId, String orderNo) {
        // 逐层检查扣量：从根级到发展渠道
        for (int i = 0; i < deductionChain.size(); i++) {
            EccOuterChannelDO currentChannel = deductionChain.get(i);
            Integer deductionRate = currentChannel.getDeductionRate();

            log.debug("检查渠道[{}]的扣量，比例: {}%", currentChannel.getChannelName(), deductionRate);

            // 执行扣量计算（使用发展渠道+检查层级的计数器）
            if (performDeduction(currentChannel, developmentChannelId)) {
                return processDeductionSuccess(deductionChain, i, currentChannel, orderNo);
            }
        }

        // 所有层级都未扣量
        log.info("订单未被扣量，订单号: {}", orderNo);
        return null;
    }

    /**
     * 处理扣量成功的情况
     *
     * @param deductionChain 扣量检查链条
     * @param currentIndex   当前扣量渠道在链条中的索引
     * @param currentChannel 当前扣量渠道
     * @param orderNo        订单号
     * @return 最终归属的渠道对象
     */
    private EccOuterChannelDO processDeductionSuccess(List<EccOuterChannelDO> deductionChain,
                                                      int currentIndex,
                                                      EccOuterChannelDO currentChannel,
                                                      String orderNo) {
        // 扣量成功，归属到上级渠道
        // 逻辑归属/扣量渠道
        Long logicalDeductionChannelId = null;
        // 最终归属/扣量渠道 (逻辑归属/扣量渠道关停了时候递归找上级)
        Long finalDeductionChannelId = null;

        if (currentIndex > 0) {
            // 有上级渠道，归属到上级
            EccOuterChannelDO logicalDeductionChannel = deductionChain.get(currentIndex - 1);
            logicalDeductionChannelId = logicalDeductionChannel.getOuterChannelId();

            if (isChannelDisabled(logicalDeductionChannel)) {
                finalDeductionChannelId = findFinalActiveChannel(deductionChain, logicalDeductionChannelId);
            } else {
                finalDeductionChannelId = logicalDeductionChannelId;
            }
        } else {
            // 没有上级渠道（当前是根级渠道），归属到平台（返回null或特殊标识）
            logicalDeductionChannelId = null; // 表示归属到平台
            finalDeductionChannelId = null;
        }

        log.info("订单扣量成功，订单号: {}, 被扣量渠道: {}, 逻辑归属渠道ID: {}, 最终归属渠道ID: {}",
                orderNo, currentChannel.getChannelName(),
                logicalDeductionChannelId != null ? logicalDeductionChannelId : PLATFORM_ATTRIBUTION,
                finalDeductionChannelId != null ? finalDeductionChannelId : PLATFORM_ATTRIBUTION);

        // 返回最终归属渠道对象
        return getFinalDeductionChannelObject(finalDeductionChannelId, logicalDeductionChannelId);
    }

    /**
     * 获取最终扣量渠道对象
     *
     * @param finalDeductionChannelId   最终归属渠道ID
     * @param logicalDeductionChannelId 逻辑归属渠道ID
     * @return 渠道对象，如果为null则表示归属到平台
     */
    private EccOuterChannelDO getFinalDeductionChannelObject(Long finalDeductionChannelId, Long logicalDeductionChannelId) {
        try {
            if (finalDeductionChannelId != null) {
                EccOuterChannelDO finalChannel = eccOuterChannelDOService.getById(finalDeductionChannelId);
                if (finalChannel != null) {
                    return finalChannel;
                }
                log.warn("最终归属渠道不存在，渠道ID: {}", finalDeductionChannelId);
            }

            if (logicalDeductionChannelId != null) {
                EccOuterChannelDO logicalChannel = eccOuterChannelDOService.getById(logicalDeductionChannelId);
                if (logicalChannel != null) {
                    return logicalChannel;
                }
                log.warn("逻辑归属渠道不存在，渠道ID: {}", logicalDeductionChannelId);
            }

            return null; // 归属到平台

        } catch (Exception e) {
            log.error("获取扣量渠道对象异常，最终归属ID: {}, 逻辑归属ID: {}",
                    finalDeductionChannelId, logicalDeductionChannelId, e);
            return null;
        }
    }


    /**
     * 执行扣量计算 - 基于Redis计数器的精确扣量算法
     *
     * @param deductionConfigChannel 扣量配置渠道
     * @param sourceChannelId        订单的发展渠道ID
     * @return true表示扣量成功
     */
    private boolean performDeduction(EccOuterChannelDO deductionConfigChannel, Long sourceChannelId) {
        if (deductionConfigChannel == null || sourceChannelId == null) {
            log.info("扣量计算参数无效，deductionConfigChannel: {}, sourceChannelId: {}",
                    deductionConfigChannel != null ? deductionConfigChannel.getOuterChannelId() : "null",
                    sourceChannelId);
            return false;
        }

        try {
            Integer channelLevel = deductionConfigChannel.getChannelLevel();
            if (channelLevel == null) {
                log.warn("渠道层级为空，无法计算扣量，渠道ID: {}", deductionConfigChannel.getOuterChannelId());
                return false;
            }

            // 检查扣量比例
            Integer deductionRate = deductionConfigChannel.getDeductionRate();
            if (deductionRate == null || deductionRate <= 0) {
                log.debug("渠道扣量比例为0或未设置，不扣量，渠道ID: {}, 扣量比例: {}",
                        deductionConfigChannel.getOuterChannelId(), deductionRate);
                return false;
            }

            // 获取扣量位置列表
            List<Integer> deductionPositions = DEDUCTION_POSITION_MAP.get(deductionRate);
            if (deductionPositions == null || deductionPositions.isEmpty()) {
                log.warn("未找到扣量比例{}%对应的扣量位置配置，渠道ID: {}",
                        deductionRate, deductionConfigChannel.getOuterChannelId());
                return false;
            }

            // Redis计数器操作
            String key = RedisKeys.getDeductionCounterByLevelKey(sourceChannelId, channelLevel);
            long orderCount = RedisUtils.incrAtomicValue(key);

            // 取模DEDUCTION_CYCLE_SIZE得到余数，然后映射到位置（1-DEDUCTION_CYCLE_SIZE）
            int remainder = (int) (orderCount % DEDUCTION_CYCLE_SIZE);
            int position = remainder == 0 ? DEDUCTION_CYCLE_SIZE : remainder;

            // 判断位置是否在扣量位置列表中
            boolean deducted = deductionPositions.contains(position);

            log.debug("扣量计算结果，发展渠道ID: {}, 扣量配置渠道ID: {}, 渠道层级: {}, 扣量比例: {}%, " +
                            "计数器值: {}, 位置: {}, 扣量位置: {}, 是否扣量: {}",
                    sourceChannelId, deductionConfigChannel.getOuterChannelId(), channelLevel,
                    deductionRate, orderCount, position, deductionPositions, deducted);

            return deducted;

        } catch (Exception e) {
            log.error("扣量计算异常，发展渠道ID: {}, 扣量配置渠道ID: {}",
                    sourceChannelId, deductionConfigChannel.getOuterChannelId(), e);
            return false;
        }
    }


    /**
     * 检查渠道是否停用
     *
     * @param channel 渠道信息
     * @return true表示停用，false表示正常或渠道为空
     */
    private boolean isChannelDisabled(EccOuterChannelDO channel) {
        if (channel == null) {
            log.warn("检查渠道状态时渠道对象为空");
            return true; // 渠道为空视为停用
        }

        Integer isDisabled = channel.getIsDisabled();
        boolean disabled = isDisabled != null && isDisabled == CHANNEL_DISABLED_FLAG;

        if (disabled) {
            log.debug("渠道已停用，渠道ID: {}, 渠道名称: {}",
                    channel.getOuterChannelId(), channel.getChannelName());
        }

        return disabled;
    }

    /**
     * 获取支持的扣量比例列表
     *
     * @return 支持的扣量比例列表
     */
    public Set<Integer> getSupportedDeductionRates() {
        return new HashSet<>(DEDUCTION_POSITION_MAP.keySet());
    }

    /**
     * 检查扣量比例是否支持
     *
     * @param deductionRate 扣量比例
     * @return true表示支持
     */
    public boolean isSupportedDeductionRate(Integer deductionRate) {
        return deductionRate != null && DEDUCTION_POSITION_MAP.containsKey(deductionRate);
    }


    /**
     * 递归查找最终的没有关停的渠道
     * 从指定渠道开始，向上级查找第一个未关停的渠道
     *
     * @param deductionChain 扣量链条
     * @param startChannelId 开始的渠道ID
     * @return 最终没有关停的渠道ID，如果都关停了则返回null（归属到平台）
     */
    private Long findFinalActiveChannel(List<EccOuterChannelDO> deductionChain, Long startChannelId) {
        if (startChannelId == null || deductionChain == null || deductionChain.isEmpty()) {
            log.warn("查找最终活跃渠道参数无效，startChannelId: {}, deductionChain: {}",
                    startChannelId, deductionChain != null ? deductionChain.size() : "null");
            return null;
        }

        try {
            // 找到起始渠道在链条中的位置
            int startIndex = -1;
            for (int i = 0; i < deductionChain.size(); i++) {
                if (deductionChain.get(i).getOuterChannelId().equals(startChannelId)) {
                    startIndex = i;
                    break;
                }
            }

            if (startIndex == -1) {
                log.warn("起始渠道在扣量链条中不存在，渠道ID: {}", startChannelId);
                return null;
            }

            // 从起始渠道开始，向上级查找第一个未关停的渠道
            for (int i = startIndex; i >= 0; i--) {
                EccOuterChannelDO channel = deductionChain.get(i);
                if (!isChannelDisabled(channel)) {
                    log.debug("找到最终活跃渠道，渠道ID: {}, 渠道名称: {}",
                            channel.getOuterChannelId(), channel.getChannelName());
                    return channel.getOuterChannelId();
                }
            }

            // 所有上级渠道都已关停
            log.info("所有上级渠道都已关停，归属到平台，起始渠道ID: {}", startChannelId);
            return null;

        } catch (Exception e) {
            log.error("查找最终活跃渠道异常，起始渠道ID: {}", startChannelId, e);
            return null;
        }
    }

    /**
     * 更新订单的扣量渠道信息
     *
     * @param order 订单对象
     */
    public void updateOrderDeductionChannel(EccNcOrderDO order) {
        if (order == null) {
            return;
        }

        Long deductionChannelId = calculateDeductionChannel(order);
        order.setDeductionChannelId(deductionChannelId);

        log.info("更新订单扣量渠道，订单号: {}, 扣量渠道ID: {}", order.getOrderNo(), deductionChannelId);
    }

    /**
     * 批量更新订单的扣量渠道信息
     *
     * @param orders 订单列表
     */
    public void batchUpdateOrderDeductionChannel(List<EccNcOrderDO> orders) {
        if (orders == null || orders.isEmpty()) {
            return;
        }

        for (EccNcOrderDO order : orders) {
            try {
                updateOrderDeductionChannel(order);
            } catch (Exception e) {
                log.error("批量更新订单扣量渠道失败，订单号: {}", order.getOrderNo(), e);
            }
        }
    }

    /**
     * 获取渠道当前的扣量计数器值
     *
     * @param channelId 渠道ID
     * @return 当前计数器值，如果不存在则返回0
     */
    public Long getChannelDeductionCounter(Long channelId) {
        String redisKey = RedisKeys.getDeductionCounterKey(channelId);
        try {
            return RedisUtils.getAtomicValue(redisKey);
        } catch (Exception e) {
            log.error("获取渠道扣量计数器失败，渠道ID: {}", channelId, e);
            return 0L;
        }
    }

    /**
     * 重置渠道的扣量计数器
     *
     * @param channelId 渠道ID
     */
    public void resetChannelDeductionCounter(Long channelId) {
        String redisKey = RedisKeys.getDeductionCounterKey(channelId);
        try {
            RedisUtils.deleteObject(redisKey);
            log.info("重置渠道扣量计数器成功，渠道ID: {}", channelId);
        } catch (Exception e) {
            log.error("重置渠道扣量计数器失败，渠道ID: {}", channelId, e);
        }
    }

    /**
     * 获取扣量比例对应的扣量位置列表
     *
     * @param deductionRate 扣量比例
     * @return 扣量位置列表
     */
    public List<Integer> getDeductionPositions(Integer deductionRate) {
        return DEDUCTION_POSITION_MAP.getOrDefault(deductionRate, Collections.emptyList());
    }

    /**
     * 预测下一个订单是否会被扣量
     *
     * @param channelId     渠道ID
     * @param deductionRate 扣量比例
     * @return true表示下一个订单会被扣量
     */
    public boolean predictNextDeduction(Long channelId, Integer deductionRate) {
        if (deductionRate == null || deductionRate <= 0) {
            return false;
        }

        List<Integer> deductionPositions = DEDUCTION_POSITION_MAP.get(deductionRate);
        if (deductionPositions == null || deductionPositions.isEmpty()) {
            return false;
        }

        Long currentCount = getChannelDeductionCounter(channelId);
        int nextRemainder = (int) ((currentCount + 1) % 20);
        int nextPosition = nextRemainder == 0 ? 20 : nextRemainder;

        return deductionPositions.contains(nextPosition);
    }

    /**
     * 获取渠道扣量统计信息
     *
     * @param channelId     渠道ID
     * @param deductionRate 扣量比例
     * @return 扣量统计信息
     */
    public Map<String, Object> getChannelDeductionStats(Long channelId, Integer deductionRate) {
        Map<String, Object> stats = new HashMap<>();

        Long currentCount = getChannelDeductionCounter(channelId);
        int currentRemainder = (int) (currentCount % 20);
        int currentPosition = currentRemainder == 0 ? 20 : currentRemainder;

        List<Integer> deductionPositions = getDeductionPositions(deductionRate);
        boolean nextWillDeduct = predictNextDeduction(channelId, deductionRate);

        // 计算已完成的周期数
        long completedCycles = currentCount / 20;

        // 计算当前周期内的扣量次数
        int currentCycleDeductions = 0;
        int currentCycleRemainder = (int) (currentCount % 20);

        for (int i = 1; i <= (currentCycleRemainder == 0 ? 20 : currentCycleRemainder); i++) {
            if (deductionPositions.contains(i)) {
                currentCycleDeductions++;
            }
        }

        stats.put("channelId", channelId);
        stats.put("deductionRate", deductionRate);
        stats.put("currentCount", currentCount);
        stats.put("currentPosition", currentPosition);
        stats.put("completedCycles", completedCycles);
        stats.put("currentCycleDeductions", currentCycleDeductions);
        stats.put("deductionPositions", deductionPositions);
        stats.put("nextWillDeduct", nextWillDeduct);

        return stats;
    }

}